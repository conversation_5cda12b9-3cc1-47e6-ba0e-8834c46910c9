import type { Metada<PERSON> } from "next";
import { Inter as FontSans } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";

import { ReactNode } from "react";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "Asger Teglgaard - B2B SaaS Marketing & Growth",
  description: "Freelance growth marketer med 10 års erfaring. AI til det kedelige, mennesker til det brillante. Book en gratis marketing therapy session.",
};

type Props = {
  children: ReactNode;
};

export default function RootLayout({ children }: Props) {
  return (
    <html lang="en">
      <body>
        {children}
      </body>
    </html>
  );
}