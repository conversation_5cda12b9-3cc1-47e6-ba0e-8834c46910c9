@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
      /* Light mode - keeping minimal light theme */
      --background: 0 0% 100%;
      --foreground: 217 33% 17%;

      --card: 0 0% 100%;
      --card-foreground: 217 33% 17%;

      --popover: 0 0% 100%;
      --popover-foreground: 217 33% 17%;

      --primary: 199 89% 48%; /* Blue #0EA5E9 */
      --primary-foreground: 0 0% 98%;

      --secondary: 174 72% 46%; /* Teal #14B8A6 */
      --secondary-foreground: 0 0% 98%;

      --muted: 210 40% 96.1%;
      --muted-foreground: 217 33% 45%;

      --accent: 38 92% 50%; /* Amber #F59E0B */
      --accent-foreground: 0 0% 9%;

      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;

      --border: 214 32% 91%;
      --input: 214 32% 91%;
      --ring: 199 89% 48%;

      --radius: 0.5rem;
    }

    .dark {
      /* Dark navy background #0F172A */
      --background: 217 33% 11%;
      --foreground: 210 40% 98%;

      --card: 217 33% 14%;
      --card-foreground: 210 40% 98%;

      --popover: 217 33% 14%;
      --popover-foreground: 210 40% 98%;

      --primary: 199 89% 48%; /* Blue #0EA5E9 */
      --primary-foreground: 217 33% 11%;

      --secondary: 174 72% 46%; /* Teal #14B8A6 */
      --secondary-foreground: 217 33% 11%;

      --muted: 217 33% 22%;
      --muted-foreground: 210 40% 60%;

      --accent: 38 92% 50%; /* Amber #F59E0B */
      --accent-foreground: 217 33% 11%;

      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;

      --border: 217 33% 22%;
      --input: 217 33% 22%;
      --ring: 199 89% 48%;
    }
  }

  @layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
  }

  h3 code {
    @apply !text-lg md:!text-xl;
  }
  
  pre {
    @apply !px-0 rounded-lg overflow-x-auto py-4
  }
   
  pre [data-line] {
    @apply px-4
  }

  code {
    @apply text-sm md:text-base !leading-loose;
  }
  
  pre > code {
    counter-reset: line;
  }
  
  code[data-theme*=" "],
  code[data-theme*=" "] span {
    color: var(--shiki-light);
    background-color: var(--shiki-light-bg);
  }
   
  @media (prefers-color-scheme: dark) {
    code[data-theme*=" "],
    code[data-theme*=" "] span {
      color: var(--shiki-dark);
      background-color: var(--shiki-dark-bg);
    }
  }
  
  code[data-line-numbers] {
    counter-reset: line;
  }
  
  code[data-line-numbers] > [data-line]::before {
    counter-increment: line;
    content: counter(line);
    @apply inline-block w-4 mr-4 text-right text-gray-500;
  }
 
  code {
    counter-reset: line;
  }
 
  code > [data-line]::before {
  counter-increment: line;
  content: counter(line);
 
  /* Other styling */
  display: inline-block;
  width: 1rem;
  margin-right: 2rem;
  text-align: right;
  color: gray;
}
 
code[data-line-numbers-max-digits="2"] > [data-line]::before {
  width: 2rem;
}
 
code[data-line-numbers-max-digits="3"] > [data-line]::before {
  width: 3rem;
}

/* Badge improvements */
.badge-minimal {
  @apply inline-flex items-center rounded-md px-3 py-1 text-xs font-medium;
  @apply bg-muted/50 text-muted-foreground border border-border/50;
  @apply hover:bg-muted/70 transition-colors;
}

/* Better card spacing */
.card-spacing {
  @apply space-y-6;
}

.section-container {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
}