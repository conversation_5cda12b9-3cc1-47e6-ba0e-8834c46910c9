{"name": "porfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.3", "@types/mdx": "^2.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "gray-matter": "^4.0.3", "lucide-react": "^0.395.0", "motion": "^12.7.4", "next": "14.2.4", "next-intl": "^4.3.4", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.1.0", "rehype-pretty-code": "^0.13.2", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "shiki": "^1.29.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.30", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "eslint": "^8.57.1", "eslint-config-next": "14.2.4", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}